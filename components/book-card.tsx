import Image from "next/image"
import Link from "next/link"
import { Star } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import type { Book } from "@/lib/books"

interface BookCardProps {
  book: Book
}

export function BookCard({ book }: BookCardProps) {
  return (
    <Link href={`/books/${book.id}`}>
      <Card className="group overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-105">
        <CardContent className="p-0">
          <div className="relative aspect-[3/4] overflow-hidden">
            <Image
              src={`/placeholder.svg?height=400&width=300`}
              alt={`Cover of ${book.title}`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="absolute bottom-4 left-4 right-4 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <p className="text-sm font-medium truncate">{book.title}</p>
              <p className="text-xs opacity-90 truncate">{book.author}</p>
            </div>
          </div>
          <div className="p-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-lg leading-tight line-clamp-2">{book.title}</h3>
              <p className="text-sm text-muted-foreground">{book.author}</p>
              <div className="flex items-center justify-between">
                <Badge variant="secondary" className="text-xs">
                  {book.genre}
                </Badge>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="text-sm font-medium">{book.rating}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
