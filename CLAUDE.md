# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run ESLint
npm run lint
```

## Architecture Overview

This is a Next.js 15 book library application using:
- **App Router** with RSC (React Server Components)
- **TypeScript** for type safety
- **Tailwind CSS** with custom theme configuration
- **shadcn/ui** component library (40+ pre-built components in `/components/ui`)
- **next-themes** for dark/light mode support

## Key Conventions

### File Structure
- `/app` - Next.js pages and layouts using App Router conventions
- `/app/books/[id]` - Dynamic routes for individual book pages
- `/components` - Reusable React components
- `/lib` - Utilities and data (currently static book data in `books.ts`)
- `/hooks` - Custom React hooks

### Data Model
Books follow this TypeScript interface:
```typescript
interface Book {
  id: string
  title: string
  author: string
  description: string
  genre: string
  publishedYear: number
  pages: number
  isbn: string
  rating: number
}
```

### Styling
- Use Tailwind CSS classes
- Theme colors are defined as CSS variables in `globals.css`
- Dark mode is class-based (`dark:` prefix)
- Import shadcn/ui components from `/components/ui`

### Component Patterns
- All shadcn/ui components are pre-configured and ready to use
- Use `cn()` utility from `/lib/utils` for conditional classes
- Components use forwardRef pattern for ref forwarding

## Important Notes

- **No Backend**: Currently uses static data from `/lib/books.ts`
- **Build Warnings**: ESLint and TypeScript errors are ignored during builds (see `next.config.mjs`)
- **Images**: Unoptimized in production for development convenience
- **Search/Filter**: UI exists but functionality is not implemented