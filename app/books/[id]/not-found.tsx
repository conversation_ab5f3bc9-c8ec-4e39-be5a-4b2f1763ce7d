import Link from "next/link"
import { BookX } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <div className="container py-16">
      <div className="text-center space-y-6">
        <BookX className="h-24 w-24 mx-auto text-muted-foreground" />
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">Book Not Found</h1>
          <p className="text-muted-foreground">Sorry, we couldn't find the book you're looking for.</p>
        </div>
        <Link href="/">
          <Button>Return to Library</Button>
        </Link>
      </div>
    </div>
  )
}
