import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ArrowLeft, Star, Calendar, BookOpen, Hash } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { getBookById } from "@/lib/books"

interface BookPageProps {
  params: Promise<{ id: string }>
}

export default async function BookPage({ params }: BookPageProps) {
  const { id } = await params
  const book = getBookById(id)

  if (!book) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <div className="mb-6">
        <Link href="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Library
          </Button>
        </Link>
      </div>

      {/* Book Details */}
      <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
        {/* Book Cover */}
        <div className="flex justify-center md:justify-start">
          <div className="relative w-80 aspect-[3/4] rounded-lg overflow-hidden shadow-2xl">
            <Image
              src={`/placeholder.svg?height=533&width=400`}
              alt={`Cover of ${book.title}`}
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>

        {/* Book Information */}
        <div className="space-y-6">
          <div>
            <Badge variant="secondary" className="mb-3">
              {book.genre}
            </Badge>
            <h1 className="text-3xl md:text-4xl font-bold mb-2">{book.title}</h1>
            <p className="text-xl text-muted-foreground mb-4">by {book.author}</p>

            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-1">
                <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                <span className="font-semibold">{book.rating}</span>
                <span className="text-muted-foreground">/ 5</span>
              </div>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{book.publishedYear}</span>
              </div>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-1">
                <BookOpen className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{book.pages} pages</span>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3">Description</h2>
            <p className="text-muted-foreground leading-relaxed">{book.description}</p>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Hash className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">ISBN:</span>
              <span className="text-sm text-muted-foreground">{book.isbn}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button size="lg" className="flex-1">
              Add to Reading List
            </Button>
            <Button variant="outline" size="lg" className="flex-1">
              Preview Book
            </Button>
          </div>
        </div>
      </div>

      {/* Additional Information */}
      <div className="mt-12 grid md:grid-cols-2 gap-8">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Book Details</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Genre:</span>
              <span>{book.genre}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Published:</span>
              <span>{book.publishedYear}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Pages:</span>
              <span>{book.pages}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">ISBN:</span>
              <span>{book.isbn}</span>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Reader Reviews</h3>
          <div className="space-y-3">
            <div className="p-4 rounded-lg bg-muted/50">
              <div className="flex items-center space-x-2 mb-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(book.rating) ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"
                      }`}
                    />
                  ))}
                </div>
                <span className="text-sm font-medium">Anonymous Reader</span>
              </div>
              <p className="text-sm text-muted-foreground">
                "A masterpiece that captures the essence of its era. Highly recommended for anyone interested in classic
                literature."
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
