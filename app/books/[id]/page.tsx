import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { ArrowLeft, Calendar, BookOpen, Globe, Heart, List } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { getBookById } from "@/lib/books"

interface BookPageProps {
  params: Promise<{ id: string }>
}

export default async function BookPage({ params }: BookPageProps) {
  const { id } = await params
  const book = getBookById(id)

  if (!book) {
    notFound()
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back Button */}
      <div className="mb-6">
        <Link href="/">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4" />
            Back to Library
          </Button>
        </Link>
      </div>

      {/* Book Details */}
      <div className="grid md:grid-cols-2 gap-8 lg:gap-12">
        {/* Book Cover */}
        <div className="flex justify-center md:justify-start">
          <div className="relative w-80 aspect-[3/4] rounded-lg overflow-hidden shadow-2xl">
            <Image
              src={`/placeholder.svg?height=533&width=400`}
              alt={`Cover of ${book.title}`}
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>

        {/* Book Information */}
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold mb-2">{book.title}</h1>
            <p className="text-xl text-muted-foreground mb-4">by {book.author}</p>

            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-1">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">Published {book.publishedYear}</span>
              </div>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-1">
                <BookOpen className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{book.pages} pages</span>
              </div>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center space-x-1">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">English</span>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-3">Description</h2>
            <p className="text-muted-foreground leading-relaxed">{book.description}</p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button size="lg" className="flex-1">
              Read Book
            </Button>
            <Button variant="outline" size="lg" className="flex-1">
              <Heart className="mr-2 h-4 w-4" />
              Add to Favorites
            </Button>
          </div>

          {/* Table of Contents */}
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-3 flex items-center">
              <List className="mr-2 h-5 w-5" />
              Table of Contents
            </h3>
            <div className="space-y-2">
              <div className="p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chapter 1: Introduction</span>
                  <span className="text-sm text-muted-foreground">Page 1</span>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chapter 2: The Beginning</span>
                  <span className="text-sm text-muted-foreground">Page 15</span>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chapter 3: Development</span>
                  <span className="text-sm text-muted-foreground">Page 42</span>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chapter 4: The Journey</span>
                  <span className="text-sm text-muted-foreground">Page 78</span>
                </div>
              </div>
              <div className="p-3 rounded-lg bg-muted/50 hover:bg-muted/70 transition-colors cursor-pointer">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Chapter 5: Conclusion</span>
                  <span className="text-sm text-muted-foreground">Page 120</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
