export interface Book {
  id: string
  title: string
  author: string
  description: string
  genre: string
  publishedYear: number
  pages: number
  isbn: string
  rating: number
}

export const books: Book[] = [
  {
    id: "1",
    title: "The Great Gatsby",
    author: "<PERSON><PERSON>",
    description:
      "A classic American novel set in the Jazz Age, exploring themes of wealth, love, and the American Dream through the eyes of narrator <PERSON>.",
    genre: "Classic Literature",
    publishedYear: 1925,
    pages: 180,
    isbn: "978-0-7432-7356-5",
    rating: 4.2,
  },
  {
    id: "2",
    title: "To Kill a Mockingbird",
    author: "<PERSON> Lee",
    description:
      "A gripping tale of racial injustice and childhood innocence in the American South, told through the perspective of young <PERSON>.",
    genre: "Classic Literature",
    publishedYear: 1960,
    pages: 376,
    isbn: "978-0-06-112008-4",
    rating: 4.3,
  },
  {
    id: "3",
    title: "1984",
    author: "<PERSON>",
    description:
      "A dystopian social science fiction novel that explores the consequences of totalitarianism, mass surveillance, and repressive regimentation.",
    genre: "Science Fiction",
    publishedYear: 1949,
    pages: 328,
    isbn: "978-0-452-28423-4",
    rating: 4.4,
  },
  {
    id: "4",
    title: "Pride and Prejudice",
    author: "Jane Austen",
    description:
      "A romantic novel that critiques the British landed gentry at the end of the 18th century, following <PERSON><PERSON> and Mr. <PERSON>.",
    genre: "Romance",
    publishedYear: 1813,
    pages: 432,
    isbn: "978-0-14-143951-8",
    rating: 4.1,
  },
  {
    id: "5",
    title: "The Catcher in the Rye",
    author: "J.<PERSON>. Salinger",
    description:
      "A controversial coming-of-age story following teenager <PERSON> Caulfield's experiences in New York City after being expelled from prep school.",
    genre: "Coming of Age",
    publishedYear: 1951,
    pages: 277,
    isbn: "978-0-316-76948-0",
    rating: 3.8,
  },
  {
    id: "6",
    title: "Dune",
    author: "Frank Herbert",
    description:
      "An epic science fiction novel set in the distant future amidst a feudal interstellar society, focusing on politics, religion, and ecology.",
    genre: "Science Fiction",
    publishedYear: 1965,
    pages: 688,
    isbn: "978-0-441-17271-9",
    rating: 4.5,
  },
  {
    id: "7",
    title: "The Lord of the Rings",
    author: "J.R.R. Tolkien",
    description:
      "An epic high fantasy novel following the quest to destroy the One Ring and defeat the Dark Lord Sauron in Middle-earth.",
    genre: "Fantasy",
    publishedYear: 1954,
    pages: 1216,
    isbn: "978-0-544-00341-5",
    rating: 4.6,
  },
  {
    id: "8",
    title: "Harry Potter and the Philosopher's Stone",
    author: "J.K. Rowling",
    description:
      "The first novel in the Harry Potter series, following a young wizard's journey as he discovers his magical heritage and attends Hogwarts.",
    genre: "Fantasy",
    publishedYear: 1997,
    pages: 223,
    isbn: "978-0-7475-3269-9",
    rating: 4.7,
  },
]

export function getBookById(id: string): Book | undefined {
  return books.find((book) => book.id === id)
}
